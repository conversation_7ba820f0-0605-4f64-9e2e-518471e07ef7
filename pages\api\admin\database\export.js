import { supabaseAdmin, getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { Parser } from 'json2csv';
import ExcelJS from 'exceljs';

// Helper function to execute SQL queries safely
async function executeSQLQuery(adminClient, query) {
  try {
    const { data, error } = await adminClient.rpc('execute_sql', {
      sql_query: query
    });

    if (error) {
      console.error('SQL execution error:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (exception) {
    console.error('SQL execution exception:', exception);
    return { data: null, error: exception };
  }
}

/**
 * Comprehensive Database Export API
 * Exports all core business tables in a unified format
 * Supports both CSV (multi-file) and JSON formats
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Database export request started`);

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  let authorized = false;
  let user = null;

  try {
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      authorized = true;
      user = { id: 'dev-user', email: '<EMAIL>' };
    } else {
      const authResult = await authenticateAdminRequest(req);
      authorized = authResult.authorized;
      user = authResult.user;
    }
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    authorized = false;
  }

  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      requestId
    });
  }

  try {
    const { format = 'json', tables = 'all', includeGuide = 'true' } = req.query;
    console.log(`[${requestId}] Export parameters: format=${format}, tables=${tables}, includeGuide=${includeGuide}`);

    // Validate format parameter
    if (!['json', 'csv', 'excel'].includes(format)) {
      return res.status(400).json({
        error: 'Invalid format parameter. Must be "json", "csv", or "excel"',
        requestId
      });
    }

    // Initialize admin client
    const adminClient = getAdminClient() || supabaseAdmin;
    if (!adminClient) {
      return res.status(500).json({
        error: 'Database connection failed',
        requestId
      });
    }

    // Define core business tables to export
    const coreTableConfigs = {
      services: {
        name: 'Services',
        query: `
          SELECT 
            s.*,
            sc.name as category_name,
            sc.description as category_description,
            sc.parent_id as category_parent_id
          FROM services s
          LEFT JOIN service_categories sc ON s.category_id = sc.id
          ORDER BY s.name
        `,
        guide: {
          description: 'Service offerings with pricing and category information',
          requiredFields: ['name', 'duration', 'price', 'color'],
          editableFields: ['name', 'description', 'duration', 'price', 'color', 'status', 'featured', 'visible_on_public', 'visible_on_pos', 'visible_on_events'],
          idFields: ['id', 'category_id'],
          relationships: ['Links to service_categories via category_id', 'Has many service_pricing_tiers']
        }
      },
      service_pricing_tiers: {
        name: 'Service Pricing Tiers',
        query: `
          SELECT 
            spt.*,
            s.name as service_name
          FROM service_pricing_tiers spt
          LEFT JOIN services s ON spt.service_id = s.id
          ORDER BY s.name, spt.sort_order
        `,
        guide: {
          description: 'Pricing variations for services (expanded format)',
          requiredFields: ['service_id', 'name', 'duration', 'price'],
          editableFields: ['name', 'description', 'duration', 'price', 'is_default', 'sort_order'],
          idFields: ['id', 'service_id'],
          relationships: ['Belongs to services via service_id']
        }
      },
      customers: {
        name: 'Customers',
        query: `
          SELECT 
            id, name, first_name, last_name, email, phone, phone_secondary,
            address, city, state, postal_code, country,
            birth_date, occupation, referral_source, customer_since,
            lifetime_value, last_booking_date, booking_count, vip,
            customer_status, customer_tier, marketing_consent,
            preferred_communication_method, communication_frequency,
            notes, created_at, updated_at
          FROM customers
          ORDER BY name
        `,
        guide: {
          description: 'Customer contact information and preferences',
          requiredFields: ['name', 'email'],
          editableFields: ['name', 'first_name', 'last_name', 'email', 'phone', 'address', 'city', 'state', 'postal_code', 'marketing_consent', 'notes'],
          idFields: ['id'],
          relationships: ['Has many bookings', 'Has many orders']
        }
      },
      bookings: {
        name: 'Bookings',
        query: `
          SELECT 
            b.*,
            c.name as customer_name,
            c.email as customer_email,
            s.name as service_name
          FROM bookings b
          LEFT JOIN customers c ON b.customer_id = c.id
          LEFT JOIN services s ON b.service_id = s.id
          ORDER BY b.start_time DESC
        `,
        guide: {
          description: 'Service bookings and appointments',
          requiredFields: ['start_time', 'end_time', 'status'],
          editableFields: ['start_time', 'end_time', 'status', 'location', 'notes', 'total_amount', 'payment_status'],
          idFields: ['id', 'customer_id', 'service_id'],
          relationships: ['Belongs to customers via customer_id', 'Belongs to services via service_id']
        }
      },
      products: {
        name: 'Products',
        query: `
          SELECT 
            p.*,
            pc.name as category_name
          FROM products p
          LEFT JOIN product_categories pc ON p.category_id = pc.id
          ORDER BY p.name
        `,
        guide: {
          description: 'Physical products and inventory items',
          requiredFields: ['name', 'price'],
          editableFields: ['name', 'description', 'sku', 'price', 'sale_price', 'cost_price', 'stock', 'is_active', 'featured'],
          idFields: ['id', 'category_id'],
          relationships: ['Links to product_categories via category_id', 'Has inventory records']
        }
      },
      orders: {
        name: 'Orders',
        query: `
          SELECT 
            o.*,
            c.name as customer_name,
            c.email as customer_email
          FROM orders o
          LEFT JOIN customers c ON o.customer_id = c.id
          ORDER BY o.order_date DESC
        `,
        guide: {
          description: 'Product orders and sales',
          requiredFields: ['order_date', 'status', 'payment_status', 'subtotal', 'total'],
          editableFields: ['status', 'payment_status', 'shipping_address', 'shipping_method', 'shipping_cost', 'tax', 'discount', 'notes'],
          idFields: ['id', 'customer_id'],
          relationships: ['Belongs to customers via customer_id', 'Has many order_items']
        }
      },
      payments: {
        name: 'Payments',
        query: `
          SELECT 
            p.*,
            CASE 
              WHEN p.booking_id IS NOT NULL THEN 'Booking'
              WHEN p.order_id IS NOT NULL THEN 'Order'
              ELSE 'Other'
            END as payment_type
          FROM payments p
          ORDER BY p.payment_date DESC
        `,
        guide: {
          description: 'Payment transactions for bookings and orders',
          requiredFields: ['amount', 'currency', 'payment_method', 'payment_status'],
          editableFields: ['payment_status', 'payment_date', 'notes'],
          idFields: ['id', 'order_id', 'booking_id'],
          relationships: ['Can belong to orders or bookings']
        }
      },
      service_categories: {
        name: 'Service Categories',
        query: `
          SELECT 
            sc.*,
            parent.name as parent_category_name
          FROM service_categories sc
          LEFT JOIN service_categories parent ON sc.parent_id = parent.id
          ORDER BY sc.name
        `,
        guide: {
          description: 'Categories for organizing services',
          requiredFields: ['name'],
          editableFields: ['name', 'description'],
          idFields: ['id', 'parent_id'],
          relationships: ['Self-referencing hierarchy via parent_id', 'Has many services']
        }
      },
      locations: {
        name: 'Locations',
        query: `
          SELECT *
          FROM locations
          ORDER BY name
        `,
        guide: {
          description: 'Business locations and venues',
          requiredFields: ['name'],
          editableFields: ['name', 'address', 'city', 'state', 'postal_code', 'country', 'is_active'],
          idFields: ['id'],
          relationships: ['Referenced by bookings and inventory']
        }
      }
    };

    // Determine which tables to export
    let tablesToExport = Object.keys(coreTableConfigs);
    if (tables !== 'all') {
      const requestedTables = tables.split(',').map(t => t.trim());
      tablesToExport = tablesToExport.filter(table => requestedTables.includes(table));
    }

    console.log(`[${requestId}] Exporting tables: ${tablesToExport.join(', ')}`);

    // Export data from each table
    const exportData = {};
    const exportMetadata = {
      timestamp: new Date().toISOString(),
      exported_by: user.email,
      tables_exported: tablesToExport,
      total_records: 0,
      format: format,
      version: '1.0'
    };

    for (const tableName of tablesToExport) {
      const config = coreTableConfigs[tableName];
      console.log(`[${requestId}] Exporting ${tableName}...`);

      try {
        const { data, error } = await adminClient.rpc('execute_sql', {
          sql_query: config.query
        });

        if (error) {
          console.error(`[${requestId}] Error exporting ${tableName}:`, error);
          // Continue with other tables
          exportData[tableName] = {
            data: [],
            error: error.message,
            guide: config.guide
          };
        } else {
          exportData[tableName] = {
            data: data || [],
            guide: config.guide,
            record_count: (data || []).length
          };
          exportMetadata.total_records += (data || []).length;
        }
      } catch (error) {
        console.error(`[${requestId}] Exception exporting ${tableName}:`, error);
        exportData[tableName] = {
          data: [],
          error: error.message,
          guide: config.guide
        };
      }
    }

    // Validate that we have data to export
    const hasData = Object.values(exportData).some(table =>
      table.data && Array.isArray(table.data) && table.data.length > 0
    );

    if (!hasData) {
      console.log(`[${requestId}] No data found for export`);
      return res.status(404).json({
        error: 'No data found for the selected tables',
        requestId,
        availableTables: Object.keys(coreTableConfigs)
      });
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `database_export_${timestamp}`;

    if (format === 'json') {
      // Return comprehensive JSON format as downloadable file
      const jsonContent = JSON.stringify({
        metadata: exportMetadata,
        tables: exportData
      }, null, 2);

      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Content-Length', Buffer.byteLength(jsonContent, 'utf8'));

      return res.status(200).send(jsonContent);
    } else if (format === 'excel') {
      // Create Excel workbook with multiple sheets
      const workbook = new ExcelJS.Workbook();
      workbook.creator = 'Ocean Soul Sparkles Database Export';
      workbook.created = new Date();

      // Add metadata sheet
      const metadataSheet = workbook.addWorksheet('Export Info');
      metadataSheet.addRow(['Export Information']);
      metadataSheet.addRow(['Timestamp', exportMetadata.timestamp]);
      metadataSheet.addRow(['Exported By', exportMetadata.exported_by]);
      metadataSheet.addRow(['Format', exportMetadata.format]);
      metadataSheet.addRow(['Total Records', exportMetadata.total_records]);
      metadataSheet.addRow(['Tables Exported', exportMetadata.tables_exported.join(', ')]);

      // Style the metadata sheet
      metadataSheet.getCell('A1').font = { bold: true, size: 14 };
      metadataSheet.getColumn('A').width = 20;
      metadataSheet.getColumn('B').width = 40;

      // Add each table as a separate sheet
      for (const tableName of tablesToExport) {
        const tableData = exportData[tableName];
        if (!tableData || !tableData.data || tableData.data.length === 0) continue;

        const sheet = workbook.addWorksheet(tableName);

        // Add guide information at the top
        if (includeGuide === 'true') {
          const guide = tableData.guide;
          sheet.addRow([`${coreTableConfigs[tableName].name.toUpperCase()} EXPORT GUIDE`]);
          sheet.addRow([]);
          sheet.addRow(['Description:', guide.description]);
          sheet.addRow(['Required Fields:', guide.requiredFields.join(', ')]);
          sheet.addRow(['Editable Fields:', guide.editableFields.join(', ')]);
          sheet.addRow(['ID Fields (Do Not Modify):', guide.idFields.join(', ')]);
          sheet.addRow(['Relationships:', guide.relationships.join('; ')]);
          sheet.addRow([]);
          sheet.addRow(['WARNING: Changes will permanently overwrite database data']);
          sheet.addRow(['Always backup before making changes']);
          sheet.addRow([]);
          sheet.addRow(['=== DATA STARTS BELOW ===']);
          sheet.addRow([]);
        }

        // Add data
        if (tableData.data.length > 0) {
          const headers = Object.keys(tableData.data[0]);
          sheet.addRow(headers);

          // Style headers
          const headerRow = sheet.lastRow;
          headerRow.font = { bold: true };
          headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
          };

          // Add data rows
          tableData.data.forEach(row => {
            const values = headers.map(header => row[header]);
            sheet.addRow(values);
          });

          // Auto-fit columns
          headers.forEach((header, index) => {
            const column = sheet.getColumn(index + 1);
            column.width = Math.min(Math.max(header.length, 10), 50);
          });
        }
      }

      // Generate Excel file buffer
      const buffer = await workbook.xlsx.writeBuffer();

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.xlsx"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Content-Length', buffer.length);

      return res.status(200).send(buffer);
    } else {
      // For CSV format, we'll return a ZIP file with multiple CSV files
      // For now, return the first table as CSV (will enhance this)
      const firstTable = tablesToExport[0];
      const tableData = exportData[firstTable];
      
      if (!tableData || !tableData.data || tableData.data.length === 0) {
        return res.status(404).json({
          error: 'No data found for export',
          requestId
        });
      }

      // Generate CSV with guide
      let csvContent = '';
      
      if (includeGuide === 'true') {
        const guide = tableData.guide;
        csvContent += [
          `🛡️ ${coreTableConfigs[firstTable].name.toUpperCase()} EXPORT GUIDE`,
          '',
          `📋 Description: ${guide.description}`,
          `🔴 Required Fields: ${guide.requiredFields.join(', ')}`,
          `🟢 Editable Fields: ${guide.editableFields.join(', ')}`,
          `🟡 ID Fields (Do Not Modify): ${guide.idFields.join(', ')}`,
          `🔗 Relationships: ${guide.relationships.join('; ')}`,
          '',
          '⚠️ WARNING: Changes will permanently overwrite database data',
          '✅ Always backup before making changes',
          '',
          '=== DATA STARTS BELOW ===',
          ''
        ].join('\n') + '\n';
      }

      // Convert data to CSV
      const fields = Object.keys(tableData.data[0] || {});
      const json2csvParser = new Parser({ fields });
      const csv = json2csvParser.parse(tableData.data);
      
      csvContent += csv;

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}_${firstTable}.csv"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      
      return res.status(200).send(csvContent);
    }

  } catch (error) {
    console.error(`[${requestId}] Critical error in database export:`, error);
    return res.status(500).json({
      error: 'Failed to export database',
      message: error.message,
      requestId
    });
  }
}
