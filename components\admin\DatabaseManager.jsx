import React, { useState, useEffect } from 'react';
import { 
  Download, 
  Upload, 
  Database, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  FileText,
  Settings,
  RefreshCw
} from 'lucide-react';

/**
 * Comprehensive Database Management Interface
 * Single interface for export, import, and backup management
 */
export default function DatabaseManager() {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [backups, setBackups] = useState([]);
  const [selectedTables, setSelectedTables] = useState(['all']);
  const [exportFormat, setExportFormat] = useState('json');
  const [importFile, setImportFile] = useState(null);
  const [updateMode, setUpdateMode] = useState('upsert');
  const [createBackup, setCreateBackup] = useState(true);
  const [lastExport, setLastExport] = useState(null);
  const [lastImport, setLastImport] = useState(null);

  // Available tables for export/import
  const availableTables = [
    { id: 'services', name: 'Services', description: 'Service offerings and pricing' },
    { id: 'customers', name: 'Customers', description: 'Customer information and contacts' },
    { id: 'bookings', name: 'Bookings', description: 'Service appointments and bookings' },
    { id: 'products', name: 'Products', description: 'Physical products and inventory' },
    { id: 'orders', name: 'Orders', description: 'Product orders and sales' },
    { id: 'payments', name: 'Payments', description: 'Payment transactions' },
    { id: 'service_categories', name: 'Service Categories', description: 'Service organization categories' },
    { id: 'locations', name: 'Locations', description: 'Business locations and venues' }
  ];

  useEffect(() => {
    loadBackups();
  }, []);

  const loadBackups = async () => {
    try {
      const response = await fetch('/api/admin/database/backups');
      if (response.ok) {
        const data = await response.json();
        setBackups(data.backups || []);
      }
    } catch (error) {
      console.error('Failed to load backups:', error);
    }
  };

  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setExportProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const tables = selectedTables.includes('all') ? 'all' : selectedTables.join(',');
      const response = await fetch(
        `/api/admin/database/export?format=${exportFormat}&tables=${tables}&includeGuide=true`
      );

      clearInterval(progressInterval);
      setExportProgress(100);

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `database_export_${new Date().toISOString().split('T')[0]}.${exportFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setLastExport({
        timestamp: new Date().toISOString(),
        format: exportFormat,
        tables: tables,
        success: true
      });

      // Reset progress after delay
      setTimeout(() => {
        setExportProgress(0);
        setIsExporting(false);
      }, 2000);

    } catch (error) {
      console.error('Export error:', error);
      setLastExport({
        timestamp: new Date().toISOString(),
        error: error.message,
        success: false
      });
      setExportProgress(0);
      setIsExporting(false);
    }
  };

  const handleImport = async () => {
    if (isImporting || !importFile) return;

    // Confirmation dialog
    const confirmed = window.confirm(
      `⚠️ WARNING: This will permanently modify your database.\n\n` +
      `${createBackup ? '✅ A backup will be created before import.\n' : '❌ No backup will be created.\n'}` +
      `Update Mode: ${updateMode}\n` +
      `File: ${importFile.name}\n\n` +
      `Are you sure you want to proceed?`
    );

    if (!confirmed) return;

    setIsImporting(true);
    setImportProgress(0);

    try {
      const formData = new FormData();
      formData.append('importFile', importFile);
      formData.append('updateMode', updateMode);
      formData.append('createBackup', createBackup.toString());
      if (!selectedTables.includes('all')) {
        formData.append('tables', selectedTables.join(','));
      }

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 5, 90));
      }, 500);

      const response = await fetch('/api/admin/database/import', {
        method: 'POST',
        body: formData
      });

      clearInterval(progressInterval);
      setImportProgress(100);

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Import failed');
      }

      setLastImport({
        timestamp: new Date().toISOString(),
        success: result.success,
        results: result.results,
        backup_id: result.backup_id
      });

      // Reload backups if one was created
      if (result.backup_created) {
        await loadBackups();
      }

      // Reset after delay
      setTimeout(() => {
        setImportProgress(0);
        setIsImporting(false);
        setImportFile(null);
      }, 3000);

    } catch (error) {
      console.error('Import error:', error);
      setLastImport({
        timestamp: new Date().toISOString(),
        error: error.message,
        success: false
      });
      setImportProgress(0);
      setIsImporting(false);
    }
  };

  const handleTableSelection = (tableId) => {
    if (tableId === 'all') {
      setSelectedTables(['all']);
    } else {
      const newSelection = selectedTables.includes('all') 
        ? [tableId]
        : selectedTables.includes(tableId)
          ? selectedTables.filter(id => id !== tableId)
          : [...selectedTables.filter(id => id !== 'all'), tableId];
      
      setSelectedTables(newSelection.length === 0 ? ['all'] : newSelection);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-3 mb-4">
          <Database className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Database Management</h1>
            <p className="text-gray-600">Export, import, and manage your complete business database</p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">Backups Available</span>
            </div>
            <p className="text-2xl font-bold text-blue-600 mt-1">{backups.length}</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-900">Last Export</span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              {lastExport ? new Date(lastExport.timestamp).toLocaleDateString() : 'Never'}
            </p>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-purple-600" />
              <span className="font-medium text-purple-900">Last Import</span>
            </div>
            <p className="text-sm text-purple-600 mt-1">
              {lastImport ? new Date(lastImport.timestamp).toLocaleDateString() : 'Never'}
            </p>
          </div>
        </div>
      </div>

      {/* Export Section */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-3 mb-6">
          <Download className="h-6 w-6 text-green-600" />
          <h2 className="text-xl font-semibold text-gray-900">Export Database</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Export Options */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Export Format
              </label>
              <select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isExporting}
              >
                <option value="json">JSON (Recommended for complete backup)</option>
                <option value="csv">CSV (Individual table export)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tables to Export
              </label>
              <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedTables.includes('all')}
                    onChange={() => handleTableSelection('all')}
                    className="mr-2"
                    disabled={isExporting}
                  />
                  <span className="font-medium">All Tables</span>
                </label>
                {availableTables.map(table => (
                  <label key={table.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedTables.includes(table.id)}
                      onChange={() => handleTableSelection(table.id)}
                      className="mr-2"
                      disabled={isExporting}
                    />
                    <div>
                      <span className="font-medium">{table.name}</span>
                      <p className="text-sm text-gray-500">{table.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Export Action */}
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Export Features</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>✅ Includes comprehensive user guides</li>
                <li>✅ Color-coded field references</li>
                <li>✅ Validation rules and examples</li>
                <li>✅ Relationship information</li>
                <li>✅ Safe for spreadsheet editing</li>
              </ul>
            </div>

            {isExporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Exporting...</span>
                  <span>{exportProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            <button
              onClick={handleExport}
              disabled={isExporting}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors"
            >
              {isExporting ? (
                <>
                  <RefreshCw className="h-5 w-5 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-5 w-5" />
                  Export Database
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Import Section */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-3 mb-6">
          <Upload className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Import Database</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Import Options */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Import File
              </label>
              <input
                type="file"
                accept=".json,.csv"
                onChange={(e) => setImportFile(e.target.files[0])}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isImporting}
              />
              {importFile && (
                <p className="text-sm text-gray-600 mt-1">
                  Selected: {importFile.name} ({formatFileSize(importFile.size)})
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Update Mode
              </label>
              <select
                value={updateMode}
                onChange={(e) => setUpdateMode(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isImporting}
              >
                <option value="upsert">Upsert (Create new, update existing)</option>
                <option value="create">Create only (Skip existing records)</option>
                <option value="update">Update only (Skip new records)</option>
              </select>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="createBackup"
                checked={createBackup}
                onChange={(e) => setCreateBackup(e.target.checked)}
                className="mr-2"
                disabled={isImporting}
              />
              <label htmlFor="createBackup" className="text-sm font-medium text-gray-700">
                Create backup before import (Recommended)
              </label>
            </div>
          </div>

          {/* Import Action */}
          <div className="space-y-4">
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <h3 className="font-medium text-yellow-900">Important Warning</h3>
              </div>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>⚠️ Import will permanently modify database</li>
                <li>🛡️ Backup is strongly recommended</li>
                <li>🔍 Review your data before importing</li>
                <li>🧪 Test with small datasets first</li>
              </ul>
            </div>

            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Importing...</span>
                  <span>{importProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${importProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            <button
              onClick={handleImport}
              disabled={isImporting || !importFile}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors"
            >
              {isImporting ? (
                <>
                  <RefreshCw className="h-5 w-5 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="h-5 w-5" />
                  Import Database
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Backup Management */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Shield className="h-6 w-6 text-purple-600" />
            <h2 className="text-xl font-semibold text-gray-900">Backup Management</h2>
          </div>
          <button
            onClick={loadBackups}
            className="text-purple-600 hover:text-purple-700 flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </button>
        </div>

        {backups.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Shield className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No backups available</p>
            <p className="text-sm">Backups are created automatically before imports</p>
          </div>
        ) : (
          <div className="space-y-3">
            {backups.slice(0, 5).map((backup) => (
              <div key={backup.backup_id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">{backup.backup_id}</p>
                    <p className="text-sm text-gray-500">
                      {new Date(backup.created_at).toLocaleString()} • 
                      {backup.total_records} records • 
                      {backup.backup_size_mb?.toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    backup.status === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {backup.status}
                  </span>
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    Restore
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
